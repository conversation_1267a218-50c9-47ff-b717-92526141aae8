<?php
defined('BASEPATH') OR exit('No direct script access allowed');
/**
 *  oxygen v2
 */
class Expense extends CI_Controller { 
  public function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('EXPENSE') || !$this->authorization->isAuthorized('EXPENSE.MODULE')) {
      redirect('dashboard', 'refresh');      
    }
    $this->load->model('ExpenseModel', 'exp');
    $this->load->library('filemanager'); 
    $this->config->load('form_elements');
  }

  public function index() {
    $data['permit_add_expense'] = $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.ADD_EXPENSE');
    $data['permit_approve_expense'] = $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.APPROVAL');
    $data['permit_view_expense_reports'] = $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS');
    $data['permit_add_expense_category'] = $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.ADD_CATEGORY');
    
    $site_url = site_url();
    $data['tiles'] = array(
        [
          'title' => 'Manage Expenses',
          'sub_title' => 'Add a New Expense',
          'icon' => 'svg_icons/add.svg',
          'url' => $site_url.'management/Expense/addExpenseData_view',
          'permission' => $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.ADD_EXPENSE')
        ],
        [
          'title' => 'Expense Category',
          'sub_title' => 'Add Expense Category',
          'icon' => 'svg_icons/expensecategory.svg',
          'url' => $site_url.'management/Expense/expense_category/',
          'permission' => $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.ADD_CATEGORY')
        ],
        // [
        //   'title' => 'My Expenses',
        //   'sub_title' => 'My Expense Status',
        //   'icon' => 'svg_icons/expenses.svg',
        //   'url' => $site_url.'management/Expense/my_expenses',
        //   'permission' => $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.ADD_EXPENSE')
        // ],
        [
          'title' => 'Approvals',
          'sub_title' => 'Approve or Reject an Expense',
          'icon' => 'svg_icons/assessment.svg',
          'url' => $site_url.'management/Expense/approveExpense',
          'permission' => $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.APPROVAL')
        ],
        [
          'title' => 'Manage Amendments',
          'sub_title' => 'Manage Amendments',
          'icon' => 'svg_icons/expensereport.svg',
          'url' => $site_url.'management/Expense/manage_amendments',
          'permission' => $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.APPROVAL_AMENDMENTS')
        ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
        [
          'title' => 'Expense Report',
          'sub_title' => 'All Expense reports',
          'icon' => 'svg_icons/expensereport.svg',
          'url' => $site_url.'management/Expense/getExpenseData',
          'permission' => $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')
        ],
        [
          'title' => 'Category Report',
          'sub_title' => '',
          'icon' => 'svg_icons/categoryreports.svg',
          'url' => $site_url.'management/Expense/expense_category_reports',
          'permission' => $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')
        ],
        [
          'title' => 'Account wise Transaction Report',
          'sub_title' => '',
          'icon' => 'svg_icons/categoryreports.svg',
          'url' => $site_url.'management/Expense/expense_account_reports',
          'permission' => $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')
        ],
        [
          'title' => 'Staff Expense Report',
          'sub_title' => '',
          'icon' => 'svg_icons/staffexpensereport.svg',
          'url' => $site_url.'management/Expense/staff_expense_reports',
          'permission' => $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')
        ],
        [
          'title' => 'Cancelled Expense Report',
          'sub_title' => '',
          'icon' => 'svg_icons/staffexpensereport.svg',
          'url' => $site_url.'management/Expense/staff_cancelled_expense_reports',
          'permission' => $this->authorization->isModuleEnabled('EXPENSE') && $this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')
        ],
        // [
        //   'title' => 'Add columns',
        //   'sub_title' => '',
        //   'icon' => 'svg_icons/staffexpensereport.svg',
        //   'url' => $site_url.'management/Expense/add_columns_db',
        //   'permission' => $this->authorization->isSuperAdmin()
        // ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);
    
    $data['title'] = 'My Expenses';
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
      }else{
        $data['main_content'] = 'management/expense/index';    	
      }
    $this->load->view('inc/template', $data);
  }

//   public function my_expenses(){
//     if (!$this->authorization->isAuthorized('EXPENSE.ADD_EXPENSE')) {
//       redirect('dashboard', 'refresh');
//     }
//     $data['my_expenses'] = $this->exp->my_expenses();
//     // echo "<pre>"; print_r($data['my_expenses']); die();
//     $data['title'] = 'My Expenses';
//       if ($this->mobile_detect->isTablet()) {
//         $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
//       }else if($this->mobile_detect->isMobile()){
//         $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
//       }else{
//         $data['main_content'] = 'management/expense/my_expenses';     	
//       }
//     $this->load->view('inc/template', $data);
// }

  public function getExpenseData() {
    if (!$this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')) {
      redirect('dashboard', 'refresh');
    }
    // $data['data']=$this->exp->getExpenseReport();
    $data['categories_list'] = $this->exp->getCategoriesList();
    $data['daterange'] = 3;
    // echo "<pre>"; print_r($data); die();
    $data['title'] = 'My Expenses';
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
      }else{
        $data['main_content'] = 'management/expense/expense_report';    	
      }
    
    $this->load->view('inc/template', $data);
  }
  
  public function expense_category_reports() {
    if (!$this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')) {
      redirect('dashboard', 'refresh');
    }
   
  
    // $from = (isset($_POST['from_date'])? $_POST['from_date']: date('Y-m-d') );  
    // $to = (isset($_POST['to_date'])? $_POST['to_date']: date('Y-m-d') );
    
    $data['from_date'] = (isset($_POST['from_date'])? $_POST['from_date']: date('Y-m-d') );  
    $data['to_date'] = (isset($_POST['to_date'])? $_POST['to_date']: date('Y-m-d') );
    $data['cat_total']=$this->exp->expense_category_reports($data['from_date'], $data['to_date']);
    $data['get_cat_sub']=$this->exp->get_category_sub_caterogy();
   
    //$data['categories_list'] = $this->exp->getCategoriesList();
    $data['daterange'] = 3;
    $data['title'] = 'My Expenses';
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
      }else{
        $data['main_content'] = 'management/expense/expense_category_reports';   	
      }
    
    $this->load->view('inc/template', $data);
  }

  public function get_sub_cat(){
    $catID  = $this->input->post('Cat_id');
    $result = $this->exp->get_sub_cat($catID);
    echo json_encode($result);

  }

  public function expense_category_reports1() {
    //$data['cat_total']=$this->exp->expense_category_reports1();
    //$data['categories_list'] = $this->exp->getCategoriesList();
    //$data['daterange'] = 3;
    if (!$this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')) {
      redirect('dashboard', 'refresh');
    }

    if(!isset($_POST['daterange']))
    redirect('management/Expense/expense_category_reports');
    $from = (isset($_POST['from_date'])? $_POST['from_date']: "0");  
    $to = (isset($_POST['to_date'])? $_POST['to_date']: "0"); 
    
    $data['cat_total']=$this->exp->expense_category_reports1($_POST['daterange'], $from, $to);
    $data['daterange'] = $_POST['daterange'];
    $data['from_date'] = $from;
    $data['to_date'] = $to;

    $data['title'] = 'My Expenses';
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
      }else{
        $data['main_content'] = 'management/expense/expense_category_reports';  	
      }
    $this->load->view('inc/template', $data);
  }


  public function staff_expense_reports() {
    if (!$this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')) {
      redirect('dashboard', 'refresh');
    }
    // $data['staff_tot']=$this->exp->expense_staff_reports();
    $data['staff_list'] = $this->exp->get_staff_list_search_expense();
    // $data['from_date'] = '$from';
    // $data['to_date'] = $to;
    //$data['categories_list'] = $this->exp->getCategoriesList();
    // $data['daterange'] = 3;
    $data['title'] = 'My Expenses';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else{
      $data['main_content'] = 'management/expense/expense_staff_reports';  	
    }
  
    $this->load->view('inc/template', $data);
  }

  public function staff_cancelled_expense_reports(){
     if (!$this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')) {
      redirect('dashboard', 'refresh');
    }
     $data['categories_list'] = $this->exp->getCategoriesList();
    
    $data['title'] = 'My Expenses';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else{
      $data['main_content'] = 'management/expense/expense_cancelled_staff_reports';  	
    }
  
    $this->load->view('inc/template', $data);

  }

  public function get_staff_all_exp(){
      $result = $this->ExpenseModel->get_staff_all_expense();
      echo json_encode($result);
    }

  public function staff_expense_reports1() {
    if (!$this->authorization->isAuthorized('EXPENSE.VIEW_REPORTS')) {
      redirect('dashboard', 'refresh');
    }
    // if(!isset($_POST['daterange']))
    // redirect('management/Expense/staff_expense_reports');
    $from = (isset($_POST['from_date'])? $_POST['from_date']: "0");  
    $to = (isset($_POST['to_date'])? $_POST['to_date']: "0"); 

    $data['staff_tot']=$this->exp->expense_staff_reports1($from, $to,$_POST['staff_list']);
    $data['staff_list'] = $this->exp->get_staff_list_search_expense();
    // $data['daterange'] = $_POST['daterange'];
    $data['from_date'] = $from;
    $data['to_date'] = $to;

    $data['title'] = 'My Expenses';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else{
      $data['main_content'] = 'management/expense/expense_staff_reports';  	
    }

    $this->load->view('inc/template', $data);
  }
  
  public function addExpenseData() {
    if (!$this->authorization->isAuthorized('EXPENSE.ADD_EXPENSE')) {
      redirect('dashboard', 'refresh');
    }
    $data['vendor_list'] = $this->exp->getVendorList();
    $data['categories_list'] = $this->exp->getCategoriesList();
    $data['catesubcat'] = $this->exp->get_categorieswisesubcatlist();
    $receiptBook =  json_decode($this->settings->getSetting('expense_vocher_set_fee_format_id'));
    if (!empty($receiptBook) && $receiptBook !='') {
      $data['voucher'] = $this->exp->get_voucher_number_receipt_book_byid($receiptBook->payment_type_cash);
    }else{
      $data['voucher'] = $this->exp->get_voucher_lastIdNumber();
    }
    $data['accounts'] = $this->exp->get_account_details();
    $data['title'] = 'My Expenses';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else{
      $data['main_content'] = 'management/expense/expense_add';	
    }
    
    $this->load->view('inc/template', $data);
   }

  public function addExpenseData_view() {
    $data['expenses'] = $this->exp->getExpenses();
    //echo "<pre>"; print_r($data['expenses']); die();
    $data['accounts'] = $this->exp->get_account_details();
     //echo "<pre>"; print_r($data['expenses']); die();
     $data['title'] = 'My Expenses';
     if ($this->mobile_detect->isTablet()) {
       $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
     }else if($this->mobile_detect->isMobile()){
       $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
     }else{
      $data['main_content'] = 'management/expense/expense_view';	
     }
  
    $this->load->view('inc/template', $data);
   }

  public function generate_vocher_edit($id){
    $data['vendor_list'] = $this->exp->getVendorList();
    $data['categories_list'] = $this->exp->getCategoriesList();
    $data['expenses_edit'] = $this->exp->edit_Expenses($id);
    // echo "<pre>"; print_r($data['expenses_edit']); die();
    $data['accounts'] = $this->exp->get_account_details();
    $data['catesubcat'] = $this->exp->get_categorieswisesubcatlist();
    //  echo "<pre>"; print_r($data['categories_list']); 
      //echo "<pre>"; print_r($data['expenses_edit']); die();
      $data['title'] = 'My Expenses';
     if ($this->mobile_detect->isTablet()) {
       $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
     }else if($this->mobile_detect->isMobile()){
       $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
     }else{
      $data['main_content'] = 'management/expense/expense_edit';	
     }
  
    $this->load->view('inc/template', $data);
  } 

  public function attached_voucher_download($id){

    $link = $this->exp->attached_voucher($id);
    $url = $this->filemanager->getFilePath($link);
    $names = explode("/", $url);
    $filename = $names[count($names) - 1];
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download($filename, $data, TRUE);
  }

  public function s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
     }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'Voucher');
    }

  /**
   * Store Data from this method.
   */
  public function saveExpenseData() {
    $paymentType = $this->input->post('payment_type');
    if (empty($paymentType) && $paymentType !=0) {
      $this->session->set_flashdata('flashError', 'Something went wrong.');
      redirect('management/Expense/addExpenseData');
    }
    // echo '<pre>'; print_r($_POST); die();

    $_POST['date'] = date('Y-m-d', strtotime($_POST['date']));
    $_POST['invoice_date'] = date('Y-m-d', strtotime($_POST['invoice_date']));

    // Get voucher file path from frontend upload (already uploaded via JavaScript)
    $voucher_file_path = $this->input->post('voucher_url');

    // Insert expense data with voucher path (if provided)
    $result = $this->exp->insertExpenseData($voucher_file_path);

    if($result){
      $receiptBook = json_decode($this->settings->getSetting('expense_vocher_set_fee_format_id'));
      if (!empty($receiptBook) && $receiptBook !='') {
        $this->exp->update_receipt_book_byid($receiptBook, $paymentType);
      }
      $this->session->set_flashdata('flashSuccess', 'Expense added successfully' . (!empty($voucher_file_path) ? ' with voucher.' : '.'));
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong while saving expense.');
    }

    if ($_POST['redirect_url'] == 1) {
      redirect('management/Expense/addExpenseData_view');
    }else{
      redirect('management/Expense/addExpenseData');
    }
   }

   public function updateExpenseDatabyId($id){
    $_POST['date'] = date('Y-m-d', strtotime($_POST['date']));
    $_POST['invoice_date'] = date('Y-m-d', strtotime($_POST['invoice_date']));
    $_POST['id'] = $id;

    // Get voucher file path from frontend upload (already uploaded via JavaScript)
    $voucher_file_path = $this->input->post('voucher_url');

    // Update expense data with new voucher path (if provided)
    $result = $this->exp->updateExpenseData($voucher_file_path, $id);

    if($result){
      $this->session->set_flashdata('flashSuccess', 'Expense updated successfully' . (!empty($voucher_file_path) ? ' with new voucher.' : '.'));
      redirect('management/Expense/addExpenseData_view');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong while updating expense.');
      redirect('management/Expense/generate_vocher_edit/'.$id);
    }
   }
  
  // public function expense_purpose() {
  //   $data['purpose_list'] = $this->exp->getPurposeList();

  //   $data['main_content'] = 'management/expense/expense_purpose';
  //   $this->load->view('inc/template', $data);
  //  }

   public function generate_vocher($expenseId){
      $data['expense'] = $this->exp->get_expense_detailsbyId($expenseId);
      $receiptpath = $this->settings->getSetting('school_short_name');
       //echo "<pre>"; print_r($data['expense']); die();
       $data['title'] = 'My Expenses';
     if ($this->mobile_detect->isTablet()) {
       $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
     }else if($this->mobile_detect->isMobile()){
       $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
     }else{
      $data['main_content'] = 'management/expense/vouchers/'.$receiptpath;
     }
      
      $this->load->view('inc/template_fee', $data);
   }

   public function soft_delete_vocher($expenseId)
   {
   
      $return= $this->exp->vocher_delete_by_id($expenseId);
      echo json_encode($return);
          
   }
   public function addExpensePurpose() {
     $this->exp->insertExpensePurpose();
     redirect('management/Expense/expense_purpose');
   }

   public function expense_category() {
    if (!$this->authorization->isAuthorized('EXPENSE.ADD_CATEGORY')) {
      redirect('dashboard', 'refresh');
    }
    $data['department'] = $this->exp->getDepartments();
    $data['categorySub'] = $this->exp->getExpCategoriesandSubList();
    $data['sub_categories_list'] = $this->exp->getSubCategoriesList();
    $data['title'] = 'My Expenses';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else{
      $data['main_content'] = 'management/expense/expense_category';
    }
   
    $this->load->view('inc/template', $data);
    }

  public function addExpenseCategory() {
    $name = $this->input->post('name');
    $department_id = $this->input->post('department_id');
    $result = $this->exp->insertExpenseCategory($name, $department_id);
    if($result == 1){
      $this->session->set_flashdata('flashSuccess', 'Successfully inserted.');
    }else if($result == '-1'){
      $this->session->set_flashdata('flashError', 'Category name already exits.');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
    }
    redirect('management/Expense/expense_category');
  }

   public function delete($id) {
     $this->db->set('status',0);  
     $this->db->where('id', $id);
     $status = $this->db->update('expense');
      return $status; 
    }

   public function changedatewisedata() {
     $dat = $this->input->post('dat');
     $data = $this->exp->get_date($dat);

    //-- for smartphones------//
    $dat1 = $this->input->post('dat1');
    $data1 = $this->exp->get_date($dat1);
    //-- for smartphones------//
    $i=1;
     $template = '';
      if(!empty($data)) {
       foreach ($data as $key => $d) {
       $template .='<tr>';
       $template .='<td>'.$i++.'</td>';
       $template .='<td>'.$d->name.'</td>';
       $template .='<td>'.date('d-m-Y ',strtotime($d->date)).'</td>';
       $template .='<td>'.$d->category_id.'</td>';
       $template .='<td>'.$d->purpose_id.'</td>';
       $template .='<td>'.$d->amount.'</td>';
       $template .='<td>'.$d->description.'</td>';
       $template .='<td>';
        if($d->voucher_url != '') {
       $template .= "<a target='_blank' href='". $this->filemanager->getFilePath($d->voucher_url)."'  
                  class='btn  btn-primary' data-placement='top'  data-toggle='tooltip' data-original-title='View Voucher'><i class='fa fa-eye'></i></a>";
       }
     $template .= '</td>';
     $template .='<td> <button onclick="delet('.$d->id.')" type="button" class="btn btn-danger btn-xs" href=           
                '.base_url('management/Expense/store/'.$d->id).'title="delete"><i class="fa fa-trash-o"> </td>';
     $template .='</tr>';
      }
     } else {
      //-- for smartphones------//
    foreach ($data1 as $key => $d) {
      $template .='<p><strong> Name :</strong>&emsp; &emsp; &emsp; ' .$d->name;
      $template .='<span class="pull-right"><strong>'.date('d-m-Y ',strtotime($d->date)). '</strong></span></p>';
      $template .='<p><strong>Category Id :</strong>&nbsp &emsp;'.$d->category_id.'</p>';
      $template .='<p><strong>Purpose Id :</strong>&nbsp &nbsp &nbsp &nbsp;'.$d->purpose_id.'</p>';
      $template .='<p><strong>Amount :</strong>&nbsp &emsp; &emsp;'.$d->amount.'</p>';
      $template .='<p><strong>Description :</strong>&nbsp &emsp;';
      $template .='<span style="white-space: pre-line;">'.$d->description.'</span></p>';
      $template .='<p><strong>Voucher :</strong>&nbsp &nbsp &emsp;';
       if($d->voucher_url != '') {
      $template .="<a target='_blank' href='". $this->filemanager->getFilePath($d->voucher_url)."'  
                  class='btn  btn-primary' data-placement='top'  data-toggle='tooltip' data-original-title='View Voucher'><i class='fa fa-eye'></i></a>";
        }
      $template .='<span class="pull-right">';
      $template .='<button onclick="delet('.$d->id.')" href='.base_url('management/Expense/store/'.$d->id).' type="button" class="btn btn-danger btn-xs">';
      $template .='<i class="fa fa-trash-o" style="font-size:15px"> </i>';
      $template .='</button></span></p>';
      $template .='<div class="col-xs-12 line" style="border:2px solid"></div><br>';
      }
      //-- for smartphones------//
    }
   print($template);
     // echo json_encode($data);
  }

  public function approveExpense(){
    if (!$this->authorization->isAuthorized('EXPENSE.APPROVAL')) {
      redirect('dashboard', 'refresh');
    }
    $data['pending_approvals'] = $this->exp->get_all_pending_approvals();
    $data['title'] = 'My Expenses';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else{
      $data['main_content'] = 'management/expense/approveExpense';
    }
    

    $this->load->view('inc/template', $data);
    
  }

  public function SaveApprovedExpense(){
    // echo $expese_id; die();
    $expense_id = $_POST['expense_id'];

    echo $this->exp->SaveApprovedExpense($expense_id);
    // if($result)
    // $this->session->set_flashdata('flashSuccess', 'Expense Approved.');
    // else
    // $this->session->set_flashdata('flashError', 'Something went wrong.');
    
    // redirect('management/Expense/approveExpense');
  }
  
  public function rejectExpense(){
    $expense_id = $_POST['expense_id'];
    //  echo $expense_id;
    $comments = $_POST['comments'];
    echo $this->exp->rejectExpense($expense_id, $comments);
  }
  
  
  public function expense_report(){
    // echo $this->acad_year->getAcadYearId() ; die();
    // if(!isset($_POST['daterange']))
    // redirect('management/Expense/getExpenseData');
    // $from = (isset($_POST['from_date'])? $_POST['from_date']: "0");  
    // $to = (isset($_POST['to_date'])? $_POST['to_date']: "0"); 
    
    // // $data['data'] = $this->exp->getExpenseReport1($_POST['daterange'], $from, $to);
    // // echo "<pre>"; print_r($data['data']); die();
    // $data['daterange'] = $_POST['daterange'];
    // $data['from_date'] = $from;
    // $data['to_date'] = $to;
    // echo '<pre>'; print_r($data); die();
    $data['title'] = 'My Expenses';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else{
      $data['main_content'] = 'management/expense/expense_report';
    }
   
    $this->load->view('inc/template', $data);

  }

  public function get_expense_report(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $payment_mode = $_POST['payment_mode'];
    $category_id = $_POST['category_id'];
    $result = $this->exp->getExpenseReport1($from_date, $to_date, $payment_mode, $category_id);
    // echo "<pre>"; print_r($result); die();
    echo json_encode($result);
  }


  public function staff_expense_staff_reports(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $staff_list = $_POST['staff_list'];
    $result = $this->exp->expense_staff_reports1($from_date, $to_date, $staff_list);
    echo json_encode($result);
  }

  public function add_sub_category(){
    $result = $this->exp->insert_sub_categories();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Sub category insert Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong..');
    }
    redirect('management/Expense/expense_category');
  }

  public function delete_sub_category($id){
      $result = $this->exp->delete_sub_categories($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Sub category delete Successfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong..');
    }
    redirect('management/Expense/expense_category');
  }

  public function get_catwise_subcat(){
    $catId = $_POST['catId'];
    $result = $this->exp->get_subcatbyCatIdwise($catId);
    echo json_encode($result);
  }
  
  public function delete_sub_categorybyExpense(){
    $subcatId = $_POST['subcatId'];
    echo $this->exp->delete_sub_categories_expense($subcatId);
  }

  public function get_vocher_number_based_on_payment(){
    $paymentType = $_POST['paymentType'];
    $receiptBook =  json_decode($this->settings->getSetting('expense_vocher_set_fee_format_id'));
    if (!empty($receiptBook) && $receiptBook !='') {
      $receiptBookId = $receiptBook->payment_type_others;
      if ($paymentType == 9) {
        $receiptBookId = $receiptBook->payment_type_cash;
      }
      if ($paymentType == 4) {
        $receiptBookId = isset($receiptBook->payment_type_cheque) ? $receiptBook->payment_type_cheque : $receiptBookId;
      }
      echo $this->exp->get_voucher_number_receipt_book_byid($receiptBookId);
    }else{
      echo $this->exp->get_vocher_number_based_on_payment_type($paymentType);
    }

    

  }

  public function vocher_soft_delete(){
    $expId = $_POST['expId'];
    $remarks = $_POST['remarks'];
    echo $this->exp->vocher_delete_by_id($expId, $remarks);
  }

  public function expense_account_reports(){
    $data['accounts'] = $this->exp->get_account_details();
    $data['title'] = 'My Expenses';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else{
      $data['main_content'] = 'management/expense/account_expense_report';
    }
    
    $this->load->view('inc/template', $data);
  }

  public function expense_account_transactions(){
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $accountId = $this->input->post('accountId');
    $payment_type = $this->input->post('payment_type');
    $data['daterange'] = 3;
    // $data['account_summary'] = $this->exp->get_account_transactions_all($accountId);
    // $account_summary=$data['account_summary'];
    // $result = $this->exp->get_account_transactions_list($from_date, $to_date, $accountId);
    $accounts = $this->exp->get_account_transactions($from_date, $to_date, $accountId);

    $ob_details = $this->exp->get_credit_details($from_date, $to_date,$accountId);
    $catSummary = $this->exp->getCatSummary($from_date, $to_date,$accountId);

    $template='';
    
    $template .= '<h2 class="card-title panel_title_new_style_staff">Account Details from ' 
           . date('d-M-Y', strtotime($from_date)) 
           . ' to ' 
           . date('d-M-Y', strtotime($to_date)) 
           . '</h2>';
    $template .= '<table id="summary" class="table table-bordered" style="width:50%">';
    $template .= '<thead>';
    $template .= '<tr>';
    $template .= '<th>#</th>';
    foreach ($ob_details as $entry) {
        $acc_name = isset($entry['acc_name']) ? $entry['acc_name'] : 'N/A';
        $template .= '<th style="max-width: 200px; text-overflow: ellipsis; overflow: hidden; white-space: nowrap;">' 
                  . htmlspecialchars($acc_name, ENT_QUOTES, 'UTF-8') 
                  . '</th>';
    }
    $template .= '</tr>';
    $template .= '</thead>';
    $template .= '<tbody>';

    $template .= '<tr><td>Opening Balance</td>';
    foreach ($ob_details as $entry) {
        $opening_balance = isset($entry['opening_balance']) ? $entry['opening_balance'] : 0;
        $formatted_balance = number_format(abs($opening_balance), 2);
        $template .= '<td>' . ($opening_balance < 0 ? "($formatted_balance)" : $formatted_balance) . '</td>';
    }
    $template .= '</tr>';

    $template .= '<tr><td>Credits</td>';
    foreach ($ob_details as $entry) {
        $total_credit = isset($entry['total_credit']) ? $entry['total_credit'] : 0;
        $template .= '<td>' . number_format($total_credit, 2) . '</td>';
    }
    $template .= '</tr>';

    $template .= '<tr><td>Debits</td>';
    foreach ($ob_details as $entry) {
        $total_debit = isset($entry['total_debit']) ? $entry['total_debit'] : 0;
        $template .= '<td>(' . number_format($total_debit, 2) . ')</td>';
    }
    $template .= '</tr>';

    $template .= '<tr><td>Closing Balance</td>';
    foreach ($ob_details as $entry) {
        $closing_balance = isset($entry['closing_balance']) ? $entry['closing_balance'] : 0;
        $formatted_balance = number_format(abs($closing_balance), 2);
        $template .= '<td>' . ($closing_balance < 0 ? "($formatted_balance)" : $formatted_balance) . '</td>';
    }
    $template .= '</tr>';

    $template .= '</tbody>';
    $template .= '</table>';
    $template .= '<br>';
    if (empty($catSummary)) {
      $template .= '<div class="no-data-display">No transactions found in this date range</div>';
       echo json_encode(array('expense'=>$template));
       return ;
     }

    $template .= '<table id="catsummary" class="table table-bordered" style="width:60%">';
    $template .= '<thead>';
    $template .= '<tr>';
    $template .= '<th>Expense Category</th>';
    $template .= '<th>Sub-Category</th>';
    $template .= '<th>Debit</th>';
    $template .= '<th>Credit</th>';
    $template .= '<th>A/C Details</th>';
    $template .= '</tr>';
    $template .= '</thead>';
    $template .= '<tbody>';
    
    foreach ($catSummary as $val) {
      $template .= '<tr>';
    $template .= '<td>'.(!empty($val->category_name) ? $val->category_name : '-').'</td>';
    $template .= '<td>'.(!empty($val->sub_category) ? $val->sub_category : '-').'</td>';
    $template .= '<td>'.(!empty($val->total_debit) && $val->total_debit != 0.00  ? number_format($val->total_debit, 2) : '-').'</td>';
    $template .= '<td>'.(!empty($val->total_credit)  && $val->total_credit != 0.00  ? number_format($val->total_credit, 2) : '-').'</td>';
    $template .= '<td>'.(!empty($val->account_number) ? $val->account_number : '-').'</td>';
    $template .= '</tr>';
    }

    $template .= '</tbody>';
    $template .= '</table>';

    $template .= '<br>';
    if (empty($accounts)) {
     $template .= '<div class="no-data-display">No transactions found in this date range</div>';
      echo json_encode(array('expense'=>$template));
      return ;
    }
                 $template .= '<table id="accountreporttable" class="table table-bordered" width="100%">
                               <thead> <tr>
                              <th width="5%">#</th>
                              <th width="8%" >Expense Date</th>
                              <th width="8%">Voucher Number</th>
                              <th width="8%">Invoice Date</th>
                              <th width="10%">Paid To</th>
                              <th width="10%">Category</th>
                              <th width="8%">Sub-Category</th>
                              <th width="20%">Narration</th>
                              <th width="10%">Payment Category</th>
                              <th width="10%">Credit</th>
                              <th width="10%">Debit</th>
                              <th width="10%" >Payment Type</th>';
                $template .= '</tr>';
                $template .= '</thead>';
                $template .= '<tbody>';

                $i = 1;
                $totalCredit = 0;
                $totalDebit = 0;
                foreach ($accounts as $val) {
                  $credit = ($val->transaction_mode == 'CREDIT') ? number_format($val->credit_amount, 2) : '-';
                  $debit = ($val->transaction_mode == 'DEBIT') ? number_format($val->amount, 2) : '-';
                  if ($val->transaction_mode == 'CREDIT') {
                    $totalCredit += $val->credit_amount;
                } elseif ($val->transaction_mode == 'DEBIT') {
                    $totalDebit += $val->amount;
                }
                  $paytype='';

                  $vendorName='';

                  if($val->payment_type == "9"){
                    $paytype="CASH";
                  }else if($val->payment_type=="4"){
                    $paytype="Cheque";
                  }else if($val->payment_type=="8"){
                    $paytype="Net Banking";
                  }
                  if($val->vendor_name == ''){
                    $vendorName = $val->name ;
                  }else{
                    $vendorName = $val->vendor_name;
                  }

                  $display_category = ucwords(str_replace('_', ' ', $val->payment_category));
                  
                    $template .= '<tr>';
                    $template .= '<td>' . ($i++) . '</td>';
                    $template .= '<td>' . date('d-M-Y', strtotime($val->made_on)) . '</td>';
                    $template .= '<td>' . (!empty($val->voucher_number) ? $val->voucher_number : '-') . '</td>';
                    $template .= '<td>' . (!empty($val->invoice_date) ? date('d-M-Y', strtotime($val->invoice_date)) : '-') . '</td>';
                    $template .= '<td>' . (!empty($vendorName) ? $vendorName : '-') . '</td>';
                    $template .= '<td>' . (!empty($val->category_name) ? $val->category_name : '-') . '</td>';
                    $template .= '<td>' . (!empty($val->sub_categories) ? $val->sub_categories : '-') . '</td>';
                    $template .= '<td>' . (!empty($val->description) ? $val->description : '-') . '</td>';
                    $template .= '<td>' . (!empty($display_category) ? $display_category : '-') . '</td>';
                    $template .= '<td>' .  $credit . '</td>';
                    $template .= '<td>' .  $debit . '</td>';
                    $template .= '<td>' . $paytype . '</td>';
                    $template .= '</tr>';
                }
                $formattedTotalCredit = number_format($totalCredit, 2);
$formattedTotalDebit = number_format($totalDebit, 2);
    $template .= '</tbody>';

$template .= '<tfoot>
                <tr>
                    <th  style="text-align:right"></th>
                    <th  style="text-align:right"></th>
                    <th  style="text-align:right"></th>
                    <th  style="text-align:right"></th>
                    <th  style="text-align:right"></th>
                    <th  style="text-align:right"></th>
                    <th  style="text-align:right"></th>
                    <th  style="text-align:right"></th>
                    <th  style="text-align:right">Total:</th>
                    <th id="total_col_9">' . $formattedTotalCredit . '</th>
                    <th id="total_col_10">' . $formattedTotalDebit . '</th>
                    <th></th>
                </tr>
             </tfoot>';
    $template .= '</table>';

      echo json_encode(array('expense'=>$template));
  }

  public function get_category_report(){
    $cat_name= $this->input->post('cat_name');
    $from_date= $this->input->post('from_date_value');
    $to_date= $this->input->post('to_date_value');
    $sub_cat_id= $this->input->post('sub_cat_name');
    $result = $this->exp->get_category_report($from_date, $to_date, $cat_name,$sub_cat_id);
    echo json_encode($result);

  }

  public function manage_amendments(){
    $data['title']='Manage Amendments';
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'staff/self_attendance/tablet_approve_neg.php';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'staff/self_attendance/mobile_checkin_neg';
    }else{
     $data['main_content'] = 'management/expense/manage_amendments_view';	
    }
    $this->load->view('inc/template', $data);
  }

  public function check_expense_voucher_num(){
    $result = $this->exp->check_expense_voucher_num($_POST['text']);
    echo json_encode($result);
  }

  public function add_expense_amendments($id){
    $data['expenses_edit'] = $this->exp->edit_Expenses($id);
    $data['expense_id']=$id;
    $data['main_content'] = 'management/expense/add_expense_amendments_view';
    $this->load->view('inc/template', $data);
  }

  public function get_expense_amendment_data(){
    $result = $this->exp->get_expense_amendment_data();
    echo json_encode($result);
  }

  public function insert_expense_amendments(){
    $result = $this->exp->insert_expense_amendments($_POST['changes'],$_POST['expense_id'],$_POST['remarks']);
    echo json_encode($result);
  }

  public function get_expense_amendment_items(){
    $result = $this->exp->get_expense_amendment_items($_POST['id']);
    echo json_encode($result);
  }

  public function approve_reject_amendment($expense_amendment_id,$voucher_number,$em_id){
    $data['expense_amendment']= $this->exp->get_expense_amendment_items($expense_amendment_id);
    $data['voucher_number']= $voucher_number;
    $data['expense_amendment_id']= $expense_amendment_id;
    $data['description']=$this->exp->get_discription_amendment($expense_amendment_id);
    $data['em_id']= $em_id;
    $data['main_content'] = 'management/expense/approve_reject_amendment_view';
    $this->load->view('inc/template', $data);
  }

  public function approve_reject_expense_amendment(){
    $result = $this->exp->approve_reject_expense_amendment($_POST);
    echo json_encode($result);
  }

  

  public function add_credit_expense(){
    $result = $this->exp->add_credit_expense();
    if($result){
      $this->session->set_flashdata('flashSuccess', 'Credit Addedd Successfully');
      redirect('management/Expense/addExpenseData_view');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
      redirect('management/Expense/addExpenseData_view');
    }
    
    
  }

  public function view_expence_details(){
    $result = $this->exp->view_expence_details();
    echo json_encode($result);
  }

  public function edit_credit_expense(){
    $result = $this->exp->edit_credit_expense();
    echo json_encode($result);
  }

  public function update_credit_expense(){
    $result = $this->exp->update_credit_expense();
    echo json_encode($result);
  }

  public function add_columns_db(){
    $result = $this->exp->add_columns_db();
    if($result){
      $this->session->set_flashdata('flashSuccess', 'Manual columns added Sucessfully');
      redirect('management/expense/index');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong.');
      redirect('management/expense/index');
    }
    $this->load->view('inc/template', $data);
  }

  function generate_cancelled_expense_report(){
    // Get POST data with validation
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $category_id = $this->input->post('category_id');

    // Debug: Log the input parameters
    log_message('debug', 'Cancelled Report - From: '.$from_date.', To: '.$to_date.', Category: '.json_encode($category_id));

    // Get cancelled expense report
    $result = $this->exp->getcancelledExpenseReport($from_date, $to_date, $category_id);

    // Debug: Log the result count
    log_message('debug', 'Cancelled Report - Result count: '.count($result));

    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($result);
  }

  /**
   * Debug function to test cancelled expenses
   */
  function debug_cancelled_expenses() {
    $count = $this->exp->getCancelledExpenseCount();
    $all_cancelled = $this->exp->getAllCancelledExpenses();

    echo "<h3>Debug Cancelled Expenses</h3>";
    echo "<p>Total cancelled expenses in database: " . $count . "</p>";
    echo "<h4>All cancelled expenses:</h4>";
    echo "<pre>";
    print_r($all_cancelled);
    echo "</pre>";
  }

 }


 ?>

 


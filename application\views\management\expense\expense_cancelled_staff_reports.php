<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('management/Expense/') ?>">Expenses</a></li>
  <li class="active"> Expense Report </li>
</ul> 

<div class="col-md-12">
  <div class="panel panel-default new-panel-style_3">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('management/Expense/') ?>"><span class="fa fa-arrow-left"></span></a>Expense Reports</h3>
        </div>
      </div>
    </div>
    <div class="panel-body" id="search">
  <form class="form-horizontal">
    <div class="row" style="margin: 0px">
      <div class="col-xs-12 col-sm-6 col-md-2">
        <div class="form-group">
          <select name="category_id" id="category_id" class="form-control select2" title="All" multiple>
            <?php foreach($categories_list as $row){ ?>
              <option value="<?= $row->id ?>"><?= $row->category_name ?></option>
            <?php } ?>
          </select>
        </div>
      </div>  

   

      <div class="col-xs-12 col-sm-6 col-md-2">
        <div id="reportrange" class="dtrange">  
          <span></span>                                          
          <input type="hidden" id="from_date">
          <input type="hidden" id="to_date">
        </div>
        <small class="col-md-12 help-text" style="padding:0;color:#aba6a6">Filter is based on Expense date</small>
      </div>

      <div class="col-xs-12 col-sm-6 col-md-2">
        <input type="button" value="Get Report" onclick="generate_cancelled_expense_report()" id="get" class="input-md btn btn-primary">
      </div>
    </div>
  </form>
</div>

  

<div class="col-md-12" id="display" style="display:none">
  <div class="panel panel-default new-panel-style_3">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="col-md-9 pl-0">
          <h3 class="card-title panel_title_new_style_staff">Expense Reports</h3>
        </div>
      
    <div class="panel-body expense" id="printArea">
    </div>
  </div>
</div>
</div>

  <script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
  <script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
  <script type="text/javascript">
    function changeDateRange(){
    var range = $('#daterange').val();
    if(range == 7)
      $("#custom_range").show();
    else
      $("#custom_range").hide();
  }

  $(document).ready(function() {
   
    $('#category_id').select2({
        placeholder: "All",
        allowClear: true
    });

    $('.date').datetimepicker({
      viewMode: 'days',
      format: 'DD-MM-YYYY'
    });
 });

  


   $('#search').on('click',function(){
      var from_date = $('#from_date').val();
      var to_date = $('#to_date').val();
      var category_id = $('#category_id').val();
      $('#fromDate').html(from_date);
      $('#toDate').html(to_date);
  });

  function generate_cancelled_expense_report() {
    $('#get').html("Please wait...").prop("disabled", true);
    // var range = $('#daterange').val();
   
    var category_id = $('#category_id').val();
    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var reportName = 'export';
    $('#fromDate').html(from_date);
    $('#toDate').html(to_date);
 

    $.ajax({
        url: '<?php echo site_url('management/expense/generate_cancelled_expense_report'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date':to_date,'category_id':category_id},
        success: function(data) {
          $('#display').show();
          $('#get').html("Get Report").prop("disabled", false);
          var exp_data = JSON.parse(data);
          if (exp_data.length > 0) {
            $(".expense").html(prepare_circular_table(exp_data)); 
            $('#reportTable').DataTable( {
              "language": {
              "search": "",
              "searchPlaceholder": "Enter Search..."
              },
              "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
              "pageLength": 10,
            dom: 'lBfrtip',
            buttons: [
              {
              extend: 'excelHtml5',
              text: 'Excel',
              filename: reportName,
              className: 'btn btn-info',
              footer: true,
              exportOptions: {
                columns: ':visible'
              },
              messageTop: function () {
                var html = '';
                html += 'Total Expenditure: '+total;
                return html;
              },
              },
              {
              extend: 'print',
              text: 'Print',
              filename: reportName,
              className: 'btn btn-info',
              footer: true,
              exportOptions: {
                columns: ':visible'
              },
              messageTop: function () {
                var html = '';
                html += 'Total Expenditure: '+total;
                return html;
              },
              },
              {
              extend: 'pdfHtml5',
              text: 'PDF',
              filename: reportName,
              className: 'btn btn-info',
              footer: true,
              exportOptions: {
                columns: ':visible'
              },
              messageTop: function () {
                var html = '';
                html += 'Total Expenditure: '+total;
                return html;
              },
              }
            ]
              });           
          }else{
             $(".expense").html('<div class="no-data-display">Result not found</div>');
          }
        
        }
    });

  }

  function prepare_circular_table(exp_data) {

  var html = '';

  var header = '';
  var thead = '';
  var body = '';
  var i=1;

  thead +='<table id="reportTable" class="table table-bordered">';
  thead +='<thead>';
  thead +='<tr>';
  thead +='<th width="3%" >#</th>';
  thead +='<th width="8%" >Expense Date</th>';
  thead +='<th width="8%" >Invoice Date</th>';
  thead +='<th width="8%" >Cheque/NetBanking Date</th>';
  thead +='<th width="8%" >Name</th>';
  
  thead +='<th width="8%" >Voucher No.</th>';
  thead +='<th width="8%" >Category</th>';
  thead +='<th width="7%" >Amount</th>';
  thead +='<th width="20%" >Narration</th>';
  thead +='<th width="8%" >Payment Type</th>';
  thead +='<th width="15%" class="print_remove" >Actions</th>';
  thead +='</thead>';
  thead 

  for(var key in exp_data){

    if (exp_data[key].cancelled_status != 1) {
      // header += '<b>Total Number of Expense :'+key+'</b><br>';
      total +=  parseFloat(exp_data[key].amount);
      var voucher_url = '<?php echo site_url('management/Expense/generate_vocher/') ?>'+exp_data[key].id;
      var delete_url = '<?php echo site_url('management/Expense/soft_delete_vocher/') ?>'+exp_data[key].id;
      body += '<tr>'
      body += '<td>'+i+'</td>';
      let defaultDate = formatDate(exp_data[key].made_on);
      let defaultDate1 = formatDate(exp_data[key].invoice_date);
      let defaultDate2 = formatDate(exp_data[key].bank_date);
      body += '<td>' + defaultDate + '</td>';
      body += '<td>' + defaultDate1 + '</td>';
      body += '<td>' + defaultDate2 + '</td>';
      body += '<td>'+((exp_data[key].name) ? exp_data[key].name: '-') +'</td>';
      
      body += '<td>'+exp_data[key].voucher_number+'</td>';
      body += '<td>'+((exp_data[key].category_name) ? exp_data[key].category_name : "-") +'</td>';
      body += '<td>'+exp_data[key].amount+'</td>';
      body += '<td>'+exp_data[key].description+'</td>';
      if (exp_data[key].payment_type=='4') {
        body += '<td>Cheque <br><strong>  Bank name: </strong>'+exp_data[key].bank_name+'<br><strong>Cheque number: </strong>'+exp_data[key].cheque_number+'<br><strong>Date: </strong>'+exp_data[key].bank_date+'</td>';
       
      }else if(exp_data[key].payment_type=='8'){
        body += '<td>Net Banking <br><strong>  Bank name: </strong>'+exp_data[key].bank_name+'<br><strong>Reference number: </strong>'+exp_data[key].cheque_number+'<br><strong>Date: </strong>'+exp_data[key].bank_date+'</td>';
        
      }else{
         body += '<td>Cash</td>';
         
      }
      var permissions = '<?php echo $this->authorization->isAuthorized('EXPENSE.CANCELLED_VOUCHER'); ?>';
      var dispaly='none';
      if(permissions){
        dispaly='inline-block';
      }
      body += '<td class="print_remove"><a target="_blank" href="'+voucher_url+'" class="btn  btn-primary" data-placement="top"  data-toggle="tooltip" data-original-title="View Voucher"><i class="fa fa-eye"></i></a> <a onclick="cancelled_voucher_confirm_box('+exp_data[key].id+')" href="javascript:void(0)"  data-toggle="tooltip" class="btn btn-danger">Cancel</a> </td>';
      body += '</tr>';
      i++;
    }else{
      var voucher_url = '<?php echo site_url('management/Expense/generate_vocher/') ?>'+exp_data[key].id;
      var delete_url = '<?php echo site_url('management/Expense/soft_delete_vocher/') ?>'+exp_data[key].id;
      body += '<tr style="color:red">';
      body += '<td>'+i+'</td>';
      let defaultDate = formatDate(exp_data[key].made_on);
      let defaultDate1 = formatDate(exp_data[key].invoice_date);
      let defaultDate2 = formatDate(exp_data[key].bank_date);
      body += '<td>' + defaultDate + '</td>';
      body += '<td>' + defaultDate1 + '</td>';
      body += '<td>' + defaultDate2 + '</td>';
      body += '<td>'+((exp_data[key].name) ? exp_data[key].name: '-') +'</td>';
    
      body += '<td>'+((exp_data[key].voucher_number) ? exp_data[key].voucher_number: '-') +'</td>';
      body += '<td>'+((exp_data[key].category_name) ? exp_data[key].category_name : "-") +'</td>';
      body += '<td>'+((exp_data[key].amount) ? exp_data[key].amount : "-") +'</td>';
      body += '<td>'+((exp_data[key].description) ? exp_data[key].description : "-") +'</td>';
      body += '<td></td>';
      body += '<td><a target="_blank" href="'+voucher_url+'" class="btn  btn-primary" data-placement="top"  data-toggle="tooltip" data-original-title="View Voucher"><i class="fa fa-eye"></i></a>  </td>';
      body += '</tr>';
      i++;
    }
    
  }
 
      body += '</tbody>';
      body += '</table>';
 
  header +='<b>Total Expenditure : '+total+'</b><br>';

  html += header + thead + body;

  return html;
}






$("#reportrange").daterangepicker({
    ranges: {
     'Today': [moment(), moment()],
     'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
      'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     //'This Month': [moment().startOf('month'), moment().endOf('month')],
      'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));


  

</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
    .dt-buttons{
    font-size: 14px;
    background:"red";
  }


	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
	}

	.dt-buttons{
		position:absolute;
		right:15px;
	}

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 15%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 11%;
		}	
	}
.dialogWide {
    width: 50% !important;
    margin-left: 25%;
  }
  .select2-container--default .select2-selection--multiple {
      padding-right: 30px; /* Make space for the dropdown symbol */
    }
    .select2-container--default .select2-selection__arrow {
      top: 50%; /* Vertically center the arrow */
      transform: translateY(-50%);
    }
</style>

